// ===== SISTEMA DE CONFIGURAÇÕES DINÂMICAS - ESTÚDIO730 =====

/**
 * @fileoverview Sistema de configurações dinâmicas refatorado seguindo princípios SOLID
 * @version 2.0.0
 * <AUTHOR>
 */

// ===== CONSTANTES E CONFIGURAÇÕES =====

const CONFIG_CONSTANTS = {
    STORAGE_KEY: 'estudio730_config',
    VERSION: '2.0.0',
    CHOICES_INIT_DELAY: 100,
    ANIMATION_DURATION: 200,
    TRANSITION_DURATION: 300,
    DEBOUNCE_DELAY: 100,
    WHATSAPP_MIN_LENGTH: 10,
    WHATSAPP_MAX_LENGTH: 15,
    MESSAGE_MAX_LENGTH: 500,
    MOBILE_BREAKPOINT: 767,
    SELECTORS: {
        CONFIG_BUTTON: '#config-button',
        CONFIG_MODAL: '#config-modal',
        EDIT_MODAL: '#edit-modal',
        LINKS_LIST: '#links-list',
        MAIN_TITLE: '#main-title',
        MAIN_SUBTITLE: '#main-subtitle'
    }
};

const ICON_OPTIONS = [
    { value: '', label: 'Selecione um ícone', color: '' },
    { value: 'fab fa-facebook', label: 'Facebook', color: '#1877f2' },
    { value: 'fab fa-instagram', label: 'Instagram', color: '#e4405f' },
    { value: 'fab fa-twitter', label: 'Twitter/X', color: '#1da1f2' },
    { value: 'fab fa-tiktok', label: 'TikTok', color: '#000000' },
    { value: 'fab fa-youtube', label: 'YouTube', color: '#ff0000' },
    { value: 'fab fa-linkedin', label: 'LinkedIn', color: '#0077b5' },
    { value: 'fab fa-whatsapp', label: 'WhatsApp', color: '#25d366' },
    { value: 'fab fa-telegram', label: 'Telegram', color: '#0088cc' },
    { value: 'fas fa-envelope', label: 'Email', color: '#ea4335' },
    { value: 'fas fa-phone', label: 'Telefone', color: '#34a853' },
    { value: 'fas fa-globe', label: 'Site/Link', color: '#6c5ce7' },
    { value: 'fas fa-map-marker-alt', label: 'Localização', color: '#4285f4' },
    { value: 'fas fa-calendar', label: 'Calendário', color: '#fbbc04' },
    { value: 'fas fa-shopping-cart', label: 'Loja', color: '#ff6900' },
    { value: 'fas fa-link', label: 'Link Genérico', color: '#6c5ce7' }
];

const LINK_TYPES = {
    DEFAULT: 'default',
    WHATSAPP: 'whatsapp',
    TELEGRAM: 'telegram',
    EMAIL: 'email',
    PHONE: 'phone'
};

const ICON_TO_LINK_TYPE = {
    'fab fa-whatsapp': LINK_TYPES.WHATSAPP,
    'fab fa-telegram': LINK_TYPES.TELEGRAM,
    'fas fa-envelope': LINK_TYPES.EMAIL,
    'fas fa-phone': LINK_TYPES.PHONE
};

// ===== CLASSES AUXILIARES =====

class ConfigStorage {
    constructor(storageKey) {
        this.storageKey = storageKey;
    }

    loadConfig(defaultConfig) {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                const config = JSON.parse(saved);
                return this.mergeConfigs(defaultConfig, config);
            }
        } catch (error) {
            console.warn('Erro ao carregar configurações:', error);
        }
        return { ...defaultConfig };
    }

    saveConfig(config, silent = false) {
        try {
            config.settings.lastModified = Date.now();
            localStorage.setItem(this.storageKey, JSON.stringify(config));
            if (!silent) {
                console.log('💾 Configurações salvas:', config);
            }
            return true;
        } catch (error) {
            console.error('Erro ao salvar configurações:', error);
            return false;
        }
    }

    mergeConfigs(defaultConfig, userConfig) {
        const merged = { ...defaultConfig };
        
        if (userConfig.links) {
            const defaultLinks = defaultConfig.links;
            const userLinks = userConfig.links;
            
            merged.links = defaultLinks.map(defaultLink => {
                const userLink = userLinks.find(ul => ul.id === defaultLink.id);
                return userLink ? { ...defaultLink, ...userLink } : defaultLink;
            });

            const customLinks = userLinks.filter(ul => !defaultLinks.some(dl => dl.id === ul.id));
            merged.links.push(...customLinks);
        }

        if (userConfig.settings) {
            merged.settings = { ...merged.settings, ...userConfig.settings };
        }

        return merged;
    }
}

class ValidationManager {
    static validateWhatsAppNumber(number) {
        if (!number || typeof number !== 'string') return false;
        
        const cleanNumber = number.trim();
        const isValidLength = cleanNumber.length >= CONFIG_CONSTANTS.WHATSAPP_MIN_LENGTH && 
                             cleanNumber.length <= CONFIG_CONSTANTS.WHATSAPP_MAX_LENGTH;
        const isNumeric = /^\d+$/.test(cleanNumber);
        
        return isValidLength && isNumeric;
    }

    static validateUrl(url) {
        if (!url || typeof url !== 'string') return false;
        
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    static validateLinkData(linkData) {
        const errors = [];
        
        if (!linkData.name || linkData.name.trim().length === 0) {
            errors.push('Nome é obrigatório');
        }
        
        if (!linkData.icon || linkData.icon.trim().length === 0) {
            errors.push('Ícone é obrigatório');
        }
        
        if (!linkData.url || linkData.url.trim().length === 0) {
            errors.push('URL é obrigatória');
        } else if (!this.validateUrl(linkData.url)) {
            errors.push('URL inválida');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

class DOMManager {
    static safeQuerySelector(selector, context = document) {
        try {
            return context.querySelector(selector);
        } catch (error) {
            console.warn(`Erro ao buscar elemento: ${selector}`, error);
            return null;
        }
    }

    static safeQuerySelectorAll(selector, context = document) {
        try {
            return context.querySelectorAll(selector);
        } catch (error) {
            console.warn(`Erro ao buscar elementos: ${selector}`, error);
            return [];
        }
    }

    static safeAddEventListener(element, event, handler, options = {}) {
        if (!element || typeof handler !== 'function') {
            console.warn('Elemento ou handler inválido para addEventListener');
            return;
        }
        
        try {
            element.addEventListener(event, handler, options);
        } catch (error) {
            console.error(`Erro ao adicionar event listener: ${event}`, error);
        }
    }

    static isMobile() {
        return window.innerWidth <= CONFIG_CONSTANTS.MOBILE_BREAKPOINT ||
               /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    static animateElement(element, styles, duration = CONFIG_CONSTANTS.ANIMATION_DURATION) {
        return new Promise(resolve => {
            if (!element) {
                resolve();
                return;
            }

            element.style.transition = `all ${duration}ms ease`;
            Object.assign(element.style, styles);
            setTimeout(resolve, duration);
        });
    }
}

class DebounceManager {
    constructor() {
        this.timeouts = new Map();
    }

    debounce(key, func, delay = CONFIG_CONSTANTS.DEBOUNCE_DELAY) {
        if (this.timeouts.has(key)) {
            clearTimeout(this.timeouts.get(key));
        }

        const timeoutId = setTimeout(() => {
            func();
            this.timeouts.delete(key);
        }, delay);

        this.timeouts.set(key, timeoutId);
    }

    cancel(key) {
        if (this.timeouts.has(key)) {
            clearTimeout(this.timeouts.get(key));
            this.timeouts.delete(key);
        }
    }

    clear() {
        this.timeouts.forEach(timeoutId => clearTimeout(timeoutId));
        this.timeouts.clear();
    }
}

// ===== CLASSE PRINCIPAL =====

class ConfigManager {
    constructor(options = {}) {
        this.storage = new ConfigStorage(options.storageKey || CONFIG_CONSTANTS.STORAGE_KEY);
        this.debouncer = new DebounceManager();
        this.defaultConfig = this.getDefaultConfig();
        this.currentConfig = this.storage.loadConfig(this.defaultConfig);
        this.choicesInstances = {};
        this.currentLinkType = LINK_TYPES.DEFAULT;
        this.init();
    }

    getDefaultConfig() {
        return {
            links: [
                {
                    id: 'whatsapp',
                    name: 'WhatsApp',
                    url: 'https://wa.me/5511999999999?text=Olá! Gostaria de agendar um horário no Estúdio730.',
                    icon: 'fab fa-whatsapp',
                    color: '#25d366',
                    visible: true,
                    removable: false,
                    order: 1
                },
                {
                    id: 'instagram',
                    name: 'Instagram',
                    url: 'https://www.instagram.com/estudio730/',
                    icon: 'fab fa-instagram',
                    color: '#e4405f',
                    visible: true,
                    removable: false,
                    order: 2
                },
                {
                    id: 'location',
                    name: 'Localização',
                    url: 'https://www.google.com/maps/search/?api=1&query=Rua das Flores, 123, São Paulo, SP',
                    icon: 'fas fa-map-marker-alt',
                    color: '#4285f4',
                    visible: true,
                    removable: false,
                    order: 3
                },
                {
                    id: 'website',
                    name: 'Site Oficial',
                    url: 'https://www.estudio730.com.br',
                    icon: 'fas fa-globe',
                    color: '#6c5ce7',
                    visible: true,
                    removable: false,
                    order: 4
                }
            ],
            settings: {
                lastModified: Date.now(),
                version: CONFIG_CONSTANTS.VERSION,
                initialName: 'Estúdio730',
                subtitle: 'Estilo e tradição em cada corte'
            }
        };
    }

    init() {
        try {
            this.populateIconSelectors();
            this.bindEvents();
            this.renderLinks();
            this.loadInitialName();
            this.initChoices();
            
            setTimeout(() => {
                this.initIconPreviewListeners();
                this.setupGlobalIconListener();
            }, CONFIG_CONSTANTS.CHOICES_INIT_DELAY);
            
            if (DOMManager.isMobile()) {
                this.initMobileOptimizations();
            }
            
            console.log('🔧 Sistema de Configurações inicializado com sucesso');
        } catch (error) {
            console.error('Erro na inicialização:', error);
        }
    }

    bindEvents() {
        // Botão de configuração
        const configButton = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.CONFIG_BUTTON);
        DOMManager.safeAddEventListener(configButton, 'click', () => this.openModal());
        DOMManager.safeAddEventListener(configButton, 'keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.openModal();
            }
        });

        // Modal principal
        const modal = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.CONFIG_MODAL);
        const closeButton = DOMManager.safeQuerySelector('#config-close');
        DOMManager.safeAddEventListener(closeButton, 'click', () => this.closeModal());
        DOMManager.safeAddEventListener(modal, 'click', (e) => {
            if (e.target === modal) this.closeModal();
        });

        // Formulários
        const expandButton = DOMManager.safeQuerySelector('#btn-expand-form');
        const cancelFormButton = DOMManager.safeQuerySelector('#btn-cancel-form');
        const addForm = DOMManager.safeQuerySelector('#add-link-form');

        DOMManager.safeAddEventListener(expandButton, 'click', () => this.toggleAddForm());
        DOMManager.safeAddEventListener(cancelFormButton, 'click', () => this.hideAddForm());
        DOMManager.safeAddEventListener(addForm, 'submit', (e) => this.handleAddLink(e));

        // Modal de edição
        const editModal = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.EDIT_MODAL);
        const editCloseButton = DOMManager.safeQuerySelector('#edit-close');
        const cancelEditButton = DOMManager.safeQuerySelector('#btn-cancel-edit');
        const editForm = DOMManager.safeQuerySelector('#edit-link-form');

        DOMManager.safeAddEventListener(editCloseButton, 'click', () => this.closeEditModal());
        DOMManager.safeAddEventListener(cancelEditButton, 'click', () => this.closeEditModal());
        DOMManager.safeAddEventListener(editForm, 'submit', (e) => this.handleEditLink(e));
        DOMManager.safeAddEventListener(editModal, 'click', (e) => {
            if (e.target === editModal) this.closeEditModal();
        });

        // Lista de links
        const linksList = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.LINKS_LIST);
        DOMManager.safeAddEventListener(linksList, 'click', (e) => this.handleLinkListClick(e));

        // Configurações
        const initialNameInput = DOMManager.safeQuerySelector('#initial-name');
        const subtitleInput = DOMManager.safeQuerySelector('#subtitle-text');

        DOMManager.safeAddEventListener(initialNameInput, 'input', (e) => {
            this.currentConfig.settings.initialName = e.target.value.trim() || 'Estúdio730';
            this.updateMainTitle();
        });

        DOMManager.safeAddEventListener(subtitleInput, 'input', (e) => {
            this.currentConfig.settings.subtitle = e.target.value.trim() || 'Estilo e tradição em cada corte';
            this.updateSubtitle();
        });

        // Botões do footer
        const saveButton = DOMManager.safeQuerySelector('#btn-save');
        const cancelButton = DOMManager.safeQuerySelector('#btn-cancel');
        const restoreButton = DOMManager.safeQuerySelector('#btn-restore');

        DOMManager.safeAddEventListener(saveButton, 'click', () => this.saveConfig());
        DOMManager.safeAddEventListener(cancelButton, 'click', () => this.closeModal());
        DOMManager.safeAddEventListener(restoreButton, 'click', () => this.restoreDefaults());

        // Tecla ESC
        DOMManager.safeAddEventListener(document, 'keydown', (e) => {
            if (e.key === 'Escape') {
                if (modal?.classList.contains('active')) {
                    this.closeModal();
                } else if (editModal?.classList.contains('active')) {
                    this.closeEditModal();
                }
            }
        });
    }

    populateIconSelectors() {
        const selectors = ['link-icon', 'edit-link-icon'];
        selectors.forEach(selectId => {
            const select = DOMManager.safeQuerySelector(`#${selectId}`);
            if (select) {
                select.innerHTML = ICON_OPTIONS.map(option =>
                    `<option value="${option.value}" data-color="${option.color}">${option.label}</option>`
                ).join('');
            }
        });
    }

    renderLinks() {
        const linksSection = DOMManager.safeQuerySelector('.links-section');
        if (!linksSection) return;

        const existingButtons = linksSection.querySelectorAll('.link-button');
        existingButtons.forEach(btn => btn.remove());

        const visibleLinks = this.currentConfig.links
            .filter(link => link.visible)
            .sort((a, b) => a.order - b.order);

        visibleLinks.forEach(link => {
            const linkElement = this.createLinkElement(link);
            linksSection.appendChild(linkElement);
        });
    }

    createLinkElement(link) {
        const linkElement = document.createElement('a');
        linkElement.href = link.url;
        linkElement.className = 'link-button';
        linkElement.target = '_blank';
        linkElement.rel = 'noopener noreferrer';
        linkElement.style.setProperty('--link-color', link.color);

        const iconSVG = this.getIconSVG(link.icon, link.color);

        linkElement.innerHTML = `
            <div class="button-content">
                ${iconSVG}
                <span class="button-text">${link.name}</span>
            </div>
        `;

        return linkElement;
    }

    getIconSVG(iconClass, color = '#ffffff') {
        const iconMap = {
            'fab fa-whatsapp': `<svg class="icon-svg" viewBox="0 0 24 24" fill="${color}"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"/></svg>`,
            'fab fa-instagram': `<svg class="icon-svg" viewBox="0 0 24 24" fill="${color}"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>`,
            'fas fa-globe': `<svg class="icon-svg" viewBox="0 0 24 24" fill="${color}"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/></svg>`,
            'fas fa-map-marker-alt': `<svg class="icon-svg" viewBox="0 0 24 24" fill="${color}"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>`,
            'fab fa-facebook': `<svg class="icon-svg" viewBox="0 0 24 24" fill="${color}"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>`,
            'fab fa-twitter': `<svg class="icon-svg" viewBox="0 0 24 24" fill="${color}"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>`
        };

        return iconMap[iconClass] || `<svg class="icon-svg" viewBox="0 0 24 24" fill="${color}"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/></svg>`;
    }

    loadInitialName() {
        const initialNameInput = DOMManager.safeQuerySelector('#initial-name');
        const subtitleInput = DOMManager.safeQuerySelector('#subtitle-text');

        if (initialNameInput) {
            initialNameInput.value = this.currentConfig.settings.initialName || 'Estúdio730';
        }

        if (subtitleInput) {
            subtitleInput.value = this.currentConfig.settings.subtitle || 'Estilo e tradição em cada corte';
        }
    }

    updateMainTitle() {
        const titleElement = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.MAIN_TITLE);
        if (titleElement) {
            titleElement.textContent = this.currentConfig.settings.initialName || 'Estúdio730';
        }
    }

    updateSubtitle() {
        const subtitleElement = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.MAIN_SUBTITLE);
        if (subtitleElement) {
            subtitleElement.textContent = this.currentConfig.settings.subtitle || 'Estilo e tradição em cada corte';
        }
    }

    openModal() {
        const modal = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.CONFIG_MODAL);
        const configButton = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.CONFIG_BUTTON);

        if (modal) {
            modal.classList.add('active');
            configButton?.setAttribute('aria-expanded', 'true');
            document.body.style.overflow = 'hidden';

            this.renderConfigLinks();
            this.loadInitialName();

            setTimeout(() => {
                this.initModalMagicEffects();
            }, CONFIG_CONSTANTS.ANIMATION_DURATION);
        }
    }

    closeModal() {
        const modal = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.CONFIG_MODAL);
        const configButton = DOMManager.safeQuerySelector(CONFIG_CONSTANTS.SELECTORS.CONFIG_BUTTON);

        if (modal) {
            if (DOMManager.isMobile()) {
                const modalContent = modal.querySelector('.config-modal-content');
                modal.classList.add('closing');
                modalContent?.classList.add('closing');

                setTimeout(() => {
                    modal.classList.remove('active', 'closing');
                    modalContent?.classList.remove('closing');
                }, CONFIG_CONSTANTS.TRANSITION_DURATION);
            } else {
                modal.classList.remove('active');
            }

            configButton?.setAttribute('aria-expanded', 'false');
            document.body.style.overflow = '';
            this.hideAddForm();
            this.clearForm();
        }
    }

    renderConfigLinks() {
        const linksList = DOMManager.safeQuerySelector('#links-list');
        if (!linksList) return;

        linksList.innerHTML = '';

        const sortedLinks = [...this.currentConfig.links].sort((a, b) => a.order - b.order);

        sortedLinks.forEach(link => {
            const linkItem = this.createConfigLinkItem(link);
            linksList.appendChild(linkItem);
        });

        setTimeout(() => {
            this.initMagicCardEffects();
        }, 100);
    }

    createConfigLinkItem(link) {
        const linkItem = document.createElement('div');
        linkItem.className = 'link-item';
        linkItem.dataset.linkId = link.id;

        const iconSVG = this.getIconSVG(link.icon, link.color);
        const truncatedUrl = this.truncateUrl(link.url);
        const visibilityIcon = link.visible ? '👁️' : '🙈';
        const visibilityText = link.visible ? 'Ocultar' : 'Mostrar';

        linkItem.innerHTML = `
            <div class="link-info">
                <div class="link-icon">
                    ${iconSVG}
                </div>
                <div class="link-details">
                    <h4 class="link-name">${link.name}</h4>
                    <p class="link-url">${truncatedUrl}</p>
                </div>
            </div>
            <div class="link-actions">
                <button class="action-btn toggle-btn" data-action="toggle" title="${visibilityText}">
                    ${visibilityIcon}
                </button>
                <button class="action-btn edit-btn" data-action="edit" title="Editar">
                    ✏️
                </button>
                ${link.removable ? '<button class="action-btn remove-btn" data-action="remove" title="Remover">🗑️</button>' : ''}
                <button class="action-btn move-btn" data-action="move-up" title="Mover para cima">⬆️</button>
                <button class="action-btn move-btn" data-action="move-down" title="Mover para baixo">⬇️</button>
            </div>
        `;

        return linkItem;
    }

    truncateUrl(url) {
        return url.length > 50 ? url.substring(0, 50) + '...' : url;
    }

    handleLinkListClick(e) {
        const target = e.target.closest('[data-action]');
        if (!target) return;

        const linkItem = target.closest('.link-item');
        if (!linkItem) return;

        const linkId = linkItem.dataset.linkId;
        const action = target.dataset.action;

        const actionMap = {
            'toggle': () => this.toggleLinkVisibility(linkId),
            'edit': () => this.openEditModal(linkId),
            'remove': () => this.removeLink(linkId),
            'move-up': () => this.moveLink(linkId, 'up'),
            'move-down': () => this.moveLink(linkId, 'down')
        };

        const actionHandler = actionMap[action];
        if (actionHandler) {
            actionHandler();
        }
    }

    toggleLinkVisibility(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);
        if (link) {
            link.visible = !link.visible;
            this.renderConfigLinks();
            this.renderLinks();
            this.storage.saveConfig(this.currentConfig, true);

            const status = link.visible ? 'visível' : 'oculto';
            this.showToast(`👁️ Link "${link.name}" agora está ${status}`, 'info');
        }
    }

    moveLink(linkId, direction) {
        const links = this.currentConfig.links;
        const sortedLinks = [...links].sort((a, b) => a.order - b.order);
        const currentIndex = sortedLinks.findIndex(l => l.id === linkId);

        if (currentIndex === -1) return;

        const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

        if (newIndex < 0 || newIndex >= sortedLinks.length) {
            const message = direction === 'up'
                ? '❌ Este item já está no topo da lista'
                : '❌ Este item já está no final da lista';
            this.showToast(message, 'warning');
            return;
        }

        const [movedLink] = sortedLinks.splice(currentIndex, 1);
        sortedLinks.splice(newIndex, 0, movedLink);

        sortedLinks.forEach((link, index) => {
            link.order = index + 1;
        });

        this.currentConfig.links = sortedLinks;
        this.renderConfigLinks();
        this.renderLinks();
        this.storage.saveConfig(this.currentConfig, true);

        const directionText = direction === 'up' ? 'para cima' : 'para baixo';
        this.showToast(`✅ Link movido ${directionText}`, 'success');
    }

    removeLink(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);

        if (!link || !link.removable) {
            this.showToast('❌ Este link não pode ser removido', 'error');
            return;
        }

        if (confirm(`Tem certeza que deseja remover o link "${link.name}"?`)) {
            this.currentConfig.links = this.currentConfig.links.filter(l => l.id !== linkId);
            this.renderConfigLinks();
            this.renderLinks();
            this.storage.saveConfig(this.currentConfig, true);
            this.showToast(`🗑️ Link "${link.name}" removido`, 'info');
        }
    }

    openEditModal(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);
        if (!link) return;

        const editIdInput = DOMManager.safeQuerySelector('#edit-link-id');
        const editNameInput = DOMManager.safeQuerySelector('#edit-link-name');

        if (editIdInput) editIdInput.value = link.id;
        if (editNameInput) editNameInput.value = link.name;

        if (this.choicesInstances.edit) {
            this.choicesInstances.edit.setChoiceByValue(link.icon);
        } else {
            const editIconSelect = DOMManager.safeQuerySelector('#edit-link-icon');
            if (editIconSelect) editIconSelect.value = link.icon;
        }

        const linkType = this.detectLinkType(link.icon);
        this.handleIconChange(link.icon, 'edit');

        setTimeout(() => {
            if (linkType === LINK_TYPES.WHATSAPP) {
                this.fillWhatsAppEditFields(link.url);
            } else {
                this.fillDefaultEditFields(link.url);
            }
        }, 200);

        const editModal = DOMManager.safeQuerySelector('#edit-modal');
        if (editModal) {
            editModal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeEditModal() {
        const editModal = DOMManager.safeQuerySelector('#edit-modal');
        if (editModal) {
            editModal.classList.remove('active');
            document.body.style.overflow = '';
            this.clearEditForm();
        }
    }

    clearEditForm() {
        const form = DOMManager.safeQuerySelector('#edit-link-form');
        if (form) {
            form.reset();
            this.deepCleanForm('edit-');

            if (this.choicesInstances.edit) {
                this.choicesInstances.edit.setValue(['']);
            }

            const preview = DOMManager.safeQuerySelector('#edit-icon-preview');
            if (preview) {
                this.updateIconPreview('edit-icon-preview', '', '');
            }

            this.currentLinkType = LINK_TYPES.DEFAULT;
        }
    }

    handleAddLink(e) {
        e.preventDefault();

        const formData = this.extractFormData('');
        if (!formData) return;

        const validation = ValidationManager.validateLinkData(formData);
        if (!validation.isValid) {
            this.showToast(`❌ ${validation.errors[0]}`, 'error');
            return;
        }

        const maxOrder = this.currentConfig.links.length > 0
            ? Math.max(...this.currentConfig.links.map(l => l.order))
            : 0;

        const newLink = {
            id: `custom_${Date.now()}`,
            name: formData.name,
            url: formData.url,
            icon: formData.icon,
            color: formData.color,
            visible: true,
            removable: true,
            order: maxOrder + 1
        };

        this.currentConfig.links.push(newLink);
        this.renderConfigLinks();
        this.renderLinks();
        this.hideAddForm();
        this.storage.saveConfig(this.currentConfig, true);
        this.showToast(`✅ Link "${formData.name}" adicionado com sucesso!`, 'success');
    }

    handleEditLink(e) {
        e.preventDefault();

        const linkId = DOMManager.safeQuerySelector('#edit-link-id')?.value;
        if (!linkId) return;

        const formData = this.extractFormData('edit-');
        if (!formData) return;

        const validation = ValidationManager.validateLinkData(formData);
        if (!validation.isValid) {
            this.showToast(`❌ ${validation.errors[0]}`, 'error');
            return;
        }

        const linkIndex = this.currentConfig.links.findIndex(l => l.id === linkId);
        if (linkIndex !== -1) {
            this.currentConfig.links[linkIndex] = {
                ...this.currentConfig.links[linkIndex],
                name: formData.name,
                url: formData.url,
                icon: formData.icon,
                color: formData.color
            };

            this.renderConfigLinks();
            this.renderLinks();
            this.closeEditModal();
            this.storage.saveConfig(this.currentConfig, true);
            this.showToast(`✅ Link "${formData.name}" atualizado com sucesso!`, 'success');
        }
    }

    extractFormData(prefix) {
        const nameInput = DOMManager.safeQuerySelector(`#${prefix}link-name`);
        const iconSelect = DOMManager.safeQuerySelector(`#${prefix}link-icon`);

        if (!nameInput || !iconSelect) return null;

        const name = nameInput.value.trim();
        const icon = iconSelect.value;
        const selectedOption = iconSelect.selectedOptions[0];
        const color = selectedOption?.dataset.color || '#6c5ce7';

        const linkType = this.detectLinkType(icon);
        let url;

        if (linkType === LINK_TYPES.WHATSAPP) {
            url = this.generateWhatsAppUrl(prefix);
        } else {
            url = this.generateDefaultUrl(prefix);
        }

        if (!url) return null;

        return { name, icon, color, url };
    }

    generateWhatsAppUrl(prefix) {
        const numberInput = DOMManager.safeQuerySelector(`#${prefix}whatsapp-number`);
        const messageInput = DOMManager.safeQuerySelector(`#${prefix}whatsapp-message`);

        if (!numberInput || !numberInput.value.trim()) {
            return null;
        }

        const number = numberInput.value.trim();
        const message = messageInput ? messageInput.value.trim() : '';

        if (!ValidationManager.validateWhatsAppNumber(number)) {
            return null;
        }

        let url = `https://wa.me/${number}`;
        if (message) {
            url += `?text=${encodeURIComponent(message)}`;
        }

        return url;
    }

    generateDefaultUrl(prefix) {
        const protocolSelect = DOMManager.safeQuerySelector(`#${prefix}link-protocol`);
        const urlInput = DOMManager.safeQuerySelector(`#${prefix}link-url`);

        if (!protocolSelect || !urlInput) return null;

        const protocol = protocolSelect.value;
        const urlPart = urlInput.value.trim();

        if (!urlPart) return null;

        return `${protocol}://${urlPart}`;
    }

    detectLinkType(iconClass) {
        return ICON_TO_LINK_TYPE[iconClass] || LINK_TYPES.DEFAULT;
    }

    toggleAddForm() {
        const container = DOMManager.safeQuerySelector('#add-link-form-container');
        if (container?.classList.contains('expanded')) {
            this.hideAddForm();
        } else {
            this.showAddForm();
        }
    }

    showAddForm() {
        const container = DOMManager.safeQuerySelector('#add-link-form-container');
        const button = DOMManager.safeQuerySelector('#btn-expand-form');

        container?.classList.add('expanded');
        button?.classList.add('hidden');

        setTimeout(() => {
            this.ensureIconListeners();
            DOMManager.safeQuerySelector('#link-name')?.focus();
        }, 300);
    }

    hideAddForm() {
        const container = DOMManager.safeQuerySelector('#add-link-form-container');
        const button = DOMManager.safeQuerySelector('#btn-expand-form');

        container?.classList.remove('expanded');
        button?.classList.remove('hidden');
        this.clearForm();
    }

    clearForm() {
        const form = DOMManager.safeQuerySelector('#add-link-form');
        if (form) {
            form.reset();
            this.deepCleanForm('');

            if (this.choicesInstances.add) {
                this.choicesInstances.add.setValue(['']);
            }

            const preview = DOMManager.safeQuerySelector('#icon-preview');
            if (preview) {
                this.updateIconPreview('icon-preview', '', '');
            }

            this.currentLinkType = LINK_TYPES.DEFAULT;
        }
    }

    deepCleanForm(prefix) {
        this.removeSpecificFields(prefix);

        if (this.debouncer) {
            Object.keys(this.debouncer.timeouts).forEach(key => {
                if (key.includes(prefix.replace('-', ''))) {
                    this.debouncer.cancel(key);
                }
            });
        }

        const updateKey = `${prefix.replace('-', '')}_updating`;
        this[updateKey] = false;

        const urlContainer = DOMManager.safeQuerySelector(`#${prefix}link-url`)?.closest('.form-group');
        if (urlContainer) {
            urlContainer.style.display = 'block';
        }
    }

    removeSpecificFields(prefix) {
        const allWhatsappContainers = DOMManager.safeQuerySelectorAll(`[id*="${prefix}whatsapp-fields"]`);
        allWhatsappContainers.forEach(container => container.remove());

        const specificFields = [
            `${prefix}whatsapp-number`,
            `${prefix}whatsapp-message`,
            `${prefix}message-counter`
        ];

        specificFields.forEach(fieldId => {
            const fields = DOMManager.safeQuerySelectorAll(`[id="${fieldId}"]`);
            fields.forEach(field => {
                const container = field.closest('.form-group') || field.closest('.whatsapp-fields');
                if (container) {
                    container.remove();
                }
            });
        });

        const orphanContainers = DOMManager.safeQuerySelectorAll('.whatsapp-fields');
        orphanContainers.forEach(container => {
            if (!container.id || container.id.includes(prefix)) {
                container.remove();
            }
        });
    }

    saveConfig(silent = false) {
        const success = this.storage.saveConfig(this.currentConfig, silent);

        if (success) {
            this.renderLinks();
            if (!silent) {
                this.showToast('✅ Configurações salvas com sucesso!', 'success');
                this.closeModal();
            }
        } else {
            this.showToast('❌ Erro ao salvar configurações', 'error');
        }
    }

    restoreDefaults() {
        const confirmMessage = 'Tem certeza que deseja restaurar as configurações padrão? ' +
                              'Todos os links personalizados serão perdidos.';

        if (confirm(confirmMessage)) {
            this.currentConfig = { ...this.defaultConfig };
            this.saveConfig();
            this.renderConfigLinks();
            this.showToast('🔄 Configurações restauradas para o padrão', 'info');
        }
    }

    showToast(message, type = 'info') {
        const existingToasts = DOMManager.safeQuerySelectorAll('.toast');
        existingToasts.forEach(toast => toast.remove());

        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        const typeColors = {
            success: '#27ae60',
            error: '#e74c3c',
            warning: '#f39c12',
            info: '#3498db'
        };

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${typeColors[type] || typeColors.info};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // Métodos para Choices.js e ícones
    initChoices() {
        const selectors = {
            '#link-icon': { previewId: 'icon-preview', key: 'add' },
            '#edit-link-icon': { previewId: 'edit-icon-preview', key: 'edit' }
        };

        Object.entries(selectors).forEach(([selector, config]) => {
            this.initChoicesForSelector(selector, config);
        });
    }

    initChoicesForSelector(selector, { previewId, key }) {
        const element = DOMManager.safeQuerySelector(selector);
        if (!element) return;

        try {
            if (this.choicesInstances[key]) {
                this.choicesInstances[key].destroy();
            }

            if (typeof Choices !== 'undefined') {
                this.choicesInstances[key] = new Choices(element, {
                    searchEnabled: false,
                    itemSelectText: 'Selecionar',
                    allowHTML: true
                });

                this.setupChoicesEventListeners(this.choicesInstances[key], previewId, key);
            }
        } catch (error) {
            console.error(`Erro ao inicializar Choices.js para ${selector}:`, error);
        }

        this.setupNativeFallback(element, previewId, key);
    }

    setupChoicesEventListeners(choicesInstance, previewId, key) {
        const events = ['change', 'choice', 'addItem'];

        events.forEach(eventType => {
            DOMManager.safeAddEventListener(choicesInstance.passedElement.element, eventType, (event) => {
                const iconClass = this.extractIconClassFromEvent(event, eventType);
                if (iconClass) {
                    this.handleIconSelection(iconClass, previewId, key);
                }
            });
        });
    }

    extractIconClassFromEvent(event, eventType) {
        switch (eventType) {
            case 'change':
                return event.detail?.value || event.target.value;
            case 'choice':
                return event.detail?.choice?.value;
            case 'addItem':
                return event.detail?.value;
            default:
                return null;
        }
    }

    handleIconSelection(iconClass, previewId, key) {
        const selectedOption = ICON_OPTIONS.find(opt => opt.value === iconClass);
        const suggestedColor = selectedOption?.color;

        this.updateIconPreview(previewId, iconClass, suggestedColor);
        this.handleIconChange(iconClass, key);
    }

    setupNativeFallback(element, previewId, key) {
        DOMManager.safeAddEventListener(element, 'change', (event) => {
            const iconClass = event.target.value;
            const selectedOption = event.target.selectedOptions[0];
            const suggestedColor = selectedOption?.dataset.color;

            this.updateIconPreview(previewId, iconClass, suggestedColor);
            this.handleIconChange(iconClass, key);
        });
    }

    updateIconPreview(previewId, iconClass, suggestedColor) {
        const preview = DOMManager.safeQuerySelector(`#${previewId}`);
        if (!preview) return;

        DOMManager.animateElement(preview, { opacity: '0.5' }, 100)
            .then(() => {
                if (iconClass && iconClass.trim() !== '') {
                    const iconSVG = this.getIconSVG(iconClass, suggestedColor);
                    preview.innerHTML = iconSVG;
                    preview.classList.add('active');
                } else {
                    preview.innerHTML = this.getDefaultIconSVG();
                    preview.classList.remove('active');
                }

                return DOMManager.animateElement(preview, { opacity: '1' }, 100);
            });
    }

    getDefaultIconSVG() {
        return `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
        </svg>`;
    }

    handleIconChange(iconClass, formType) {
        const linkType = this.detectLinkType(iconClass);
        this.currentLinkType = linkType;

        const debounceKey = `${formType}_${linkType}`;
        this.debouncer.debounce(debounceKey, () => {
            this.updateFormFields(linkType, formType);
        });
    }

    updateFormFields(linkType, formType) {
        const isEditForm = formType === 'edit';
        const formPrefix = isEditForm ? 'edit-' : '';

        const updateKey = `${formType}_updating`;
        if (this[updateKey]) return;

        this[updateKey] = true;

        try {
            const urlContainer = this.getUrlContainer(formPrefix);
            if (!urlContainer) return;

            this.removeSpecificFields(formPrefix);
            this.transitionFormFields(urlContainer, linkType, formPrefix);
        } finally {
            setTimeout(() => {
                this[updateKey] = false;
            }, CONFIG_CONSTANTS.ANIMATION_DURATION);
        }
    }

    getUrlContainer(formPrefix) {
        const urlInput = DOMManager.safeQuerySelector(`#${formPrefix}link-url`);
        return urlInput?.closest('.form-group');
    }

    async transitionFormFields(urlContainer, linkType, formPrefix) {
        await DOMManager.animateElement(urlContainer, {
            transform: 'translateY(-10px)',
            opacity: '0.3'
        });

        if (linkType === LINK_TYPES.WHATSAPP) {
            this.createWhatsAppFields(urlContainer, formPrefix);
        } else {
            this.createDefaultFields(urlContainer, formPrefix);
            this.clearWhatsAppFields(formPrefix);
        }

        await DOMManager.animateElement(urlContainer, {
            transform: 'translateY(0)',
            opacity: '1'
        });
    }

    createWhatsAppFields(urlContainer, formPrefix) {
        const existingContainer = DOMManager.safeQuerySelector(`#${formPrefix}whatsapp-fields`);
        if (existingContainer) return;

        urlContainer.style.display = 'none';

        const protocolSelect = DOMManager.safeQuerySelector(`#${formPrefix}link-protocol`);
        const urlInput = DOMManager.safeQuerySelector(`#${formPrefix}link-url`);

        if (protocolSelect) protocolSelect.removeAttribute('required');
        if (urlInput) urlInput.removeAttribute('required');

        const whatsappContainer = document.createElement('div');
        whatsappContainer.className = 'whatsapp-fields';
        whatsappContainer.id = `${formPrefix}whatsapp-fields`;

        whatsappContainer.innerHTML = `
            <div class="form-group">
                <label for="${formPrefix}whatsapp-number">Número do WhatsApp *</label>
                <input
                    type="tel"
                    id="${formPrefix}whatsapp-number"
                    placeholder="5575991929294"
                    required
                    pattern="[0-9]{10,15}"
                    title="Digite apenas números (10-15 dígitos)"
                >
                <small class="field-hint">Digite apenas números, incluindo código do país e DDD</small>
            </div>

            <div class="form-group">
                <label for="${formPrefix}whatsapp-message">Mensagem (opcional)</label>
                <textarea
                    id="${formPrefix}whatsapp-message"
                    placeholder="Olá! Gostaria de agendar um horário..."
                    rows="3"
                    maxlength="500"
                ></textarea>
                <small class="field-hint">Mensagem que será enviada automaticamente</small>
            </div>
        `;

        urlContainer.parentNode.insertBefore(whatsappContainer, urlContainer.nextSibling);
        this.setupWhatsAppValidation(formPrefix);
    }

    createDefaultFields(urlContainer, formPrefix) {
        urlContainer.style.display = 'block';

        const protocolSelect = DOMManager.safeQuerySelector(`#${formPrefix}link-protocol`);
        const urlInput = DOMManager.safeQuerySelector(`#${formPrefix}link-url`);

        if (protocolSelect) {
            protocolSelect.style.display = 'block';
            protocolSelect.setAttribute('required', 'required');
            if (!protocolSelect.value) {
                protocolSelect.value = 'https';
            }
        }

        if (urlInput) {
            urlInput.style.display = 'block';
            urlInput.setAttribute('required', 'required');
            urlInput.classList.remove('valid', 'invalid');
            urlInput.style.borderColor = '';
            urlInput.style.boxShadow = '';
        }
    }

    setupWhatsAppValidation(formPrefix) {
        const numberInput = DOMManager.safeQuerySelector(`#${formPrefix}whatsapp-number`);
        const messageInput = DOMManager.safeQuerySelector(`#${formPrefix}whatsapp-message`);

        if (numberInput) {
            DOMManager.safeAddEventListener(numberInput, 'input', (e) => {
                let value = e.target.value.replace(/\D/g, '');
                e.target.value = value;
                this.validateWhatsAppNumber(numberInput);
            });

            DOMManager.safeAddEventListener(numberInput, 'blur', () => {
                this.validateWhatsAppNumber(numberInput);
            });
        }

        if (messageInput) {
            DOMManager.safeAddEventListener(messageInput, 'input', () => {
                this.updateMessageCounter(messageInput, formPrefix);
            });
        }
    }

    validateWhatsAppNumber(input) {
        const value = input.value.trim();
        const isValid = ValidationManager.validateWhatsAppNumber(value);

        input.classList.remove('valid', 'invalid');

        if (value !== '') {
            input.classList.add(isValid ? 'valid' : 'invalid');

            if (isValid) {
                input.style.borderColor = '#27ae60';
                input.style.boxShadow = '0 0 0 2px rgba(39, 174, 96, 0.2)';
            } else {
                input.style.borderColor = '#e74c3c';
                input.style.boxShadow = '0 0 0 2px rgba(231, 76, 60, 0.2)';
            }
        } else {
            input.style.borderColor = '';
            input.style.boxShadow = '';
        }

        return isValid;
    }

    updateMessageCounter(textarea, formPrefix) {
        const maxLength = CONFIG_CONSTANTS.MESSAGE_MAX_LENGTH;
        const currentLength = textarea.value.length;

        let counter = DOMManager.safeQuerySelector(`#${formPrefix}message-counter`);
        if (!counter) {
            counter = document.createElement('small');
            counter.id = `${formPrefix}message-counter`;
            counter.className = 'character-counter';
            textarea.parentNode.appendChild(counter);
        }

        counter.textContent = `${currentLength}/${maxLength} caracteres`;
        counter.style.color = currentLength > maxLength * 0.9 ? '#e74c3c' : '#666';
    }

    clearWhatsAppFields(formPrefix) {
        const numberInput = DOMManager.safeQuerySelector(`#${formPrefix}whatsapp-number`);
        const messageInput = DOMManager.safeQuerySelector(`#${formPrefix}whatsapp-message`);

        if (numberInput) {
            numberInput.value = '';
            numberInput.classList.remove('valid', 'invalid');
            numberInput.style.borderColor = '';
            numberInput.style.boxShadow = '';
        }

        if (messageInput) {
            messageInput.value = '';
        }

        const counter = DOMManager.safeQuerySelector(`#${formPrefix}message-counter`);
        if (counter) {
            counter.remove();
        }
    }

    // Métodos auxiliares para preenchimento de formulários
    fillWhatsAppEditFields(url) {
        const whatsappData = this.parseWhatsAppUrl(url);

        const numberInput = DOMManager.safeQuerySelector('edit-whatsapp-number');
        const messageInput = DOMManager.safeQuerySelector('edit-whatsapp-message');

        if (numberInput && whatsappData.number) {
            numberInput.value = whatsappData.number;
        }
        if (messageInput && whatsappData.message) {
            messageInput.value = whatsappData.message;
        }
    }

    fillDefaultEditFields(url) {
        const { protocol, urlPart } = this.parseUrl(url);

        const protocolSelect = DOMManager.safeQuerySelector('#edit-link-protocol');
        const urlInput = DOMManager.safeQuerySelector('#edit-link-url');

        if (protocolSelect) protocolSelect.value = protocol;
        if (urlInput) urlInput.value = urlPart;
    }

    parseWhatsAppUrl(url) {
        const match = url.match(/wa\.me\/(\d+)(?:\?text=(.*))?/);
        return {
            number: match ? match[1] : '',
            message: match && match[2] ? decodeURIComponent(match[2]) : ''
        };
    }

    parseUrl(url) {
        try {
            const urlObj = new URL(url);
            return {
                protocol: urlObj.protocol.replace(':', ''),
                urlPart: url.replace(`${urlObj.protocol}//`, '')
            };
        } catch {
            return {
                protocol: 'https',
                urlPart: url
            };
        }
    }

    // Métodos para efeitos Magic UI
    initModalMagicEffects() {
        this.initSpotlightEffect();
        this.initRippleEffects();
        this.initMagicCardEffects();
    }

    initSpotlightEffect() {
        const modal = DOMManager.safeQuerySelector('.config-modal-content');
        if (!modal) return;

        DOMManager.safeAddEventListener(modal, 'mousemove', (e) => {
            const rect = modal.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;

            modal.style.setProperty('--mouse-x', `${x}%`);
            modal.style.setProperty('--mouse-y', `${y}%`);
        });
    }

    initRippleEffects() {
        const buttons = DOMManager.safeQuerySelectorAll('.config-modal button, .edit-modal button');

        buttons.forEach(button => {
            DOMManager.safeAddEventListener(button, 'click', (e) => {
                this.createRippleEffect(e, button);
            });
        });
    }

    initMagicCardEffects() {
        const linkItems = DOMManager.safeQuerySelectorAll('.link-item');

        linkItems.forEach(item => {
            DOMManager.safeAddEventListener(item, 'mousemove', (e) => {
                const rect = item.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 100;
                const y = ((e.clientY - rect.top) / rect.height) * 100;

                item.style.setProperty('--mouse-x', `${x}%`);
                item.style.setProperty('--mouse-y', `${y}%`);
            });
        });
    }

    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            pointer-events: none;
        `;

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        setTimeout(() => ripple.remove(), 600);
    }

    // Métodos auxiliares finais
    initIconPreviewListeners() {
        const iconSelectors = [
            { selectId: 'link-icon', previewId: 'icon-preview', formType: 'add' },
            { selectId: 'edit-link-icon', previewId: 'edit-icon-preview', formType: 'edit' }
        ];

        iconSelectors.forEach(({ selectId, previewId, formType }) => {
            const select = DOMManager.safeQuerySelector(`#${selectId}`);
            if (!select) return;

            const currentValue = select.value;
            if (currentValue) {
                const selectedOption = select.selectedOptions[0];
                const suggestedColor = selectedOption?.dataset.color;
                this.updateIconPreview(previewId, currentValue, suggestedColor);
            }

            if (!this.choicesInstances[formType]) {
                this.setupNativeFallback(select, previewId, formType);
            }
        });
    }

    setupGlobalIconListener() {
        DOMManager.safeAddEventListener(document, 'change', (event) => {
            this.handleGlobalIconChange(event);
        });

        DOMManager.safeAddEventListener(document, 'input', (event) => {
            this.handleGlobalIconInput(event);
        });
    }

    handleGlobalIconChange(event) {
        const target = event.target;

        if (!this.isIconSelector(target)) return;

        const { formType, iconClass, previewId } = this.extractIconData(target);

        if (iconClass) {
            const selectedOption = ICON_OPTIONS.find(opt => opt.value === iconClass);
            const suggestedColor = selectedOption?.color;

            this.updateIconPreview(previewId, iconClass, suggestedColor);
            this.handleIconChange(iconClass, formType);
        }
    }

    handleGlobalIconInput(event) {
        const target = event.target;

        if (!this.isIconSelector(target)) return;

        const { formType, iconClass } = this.extractIconData(target);

        if (iconClass) {
            this.handleIconChange(iconClass, formType);
        }
    }

    isIconSelector(element) {
        return element.id === 'link-icon' || element.id === 'edit-link-icon';
    }

    extractIconData(element) {
        const formType = element.id === 'link-icon' ? 'add' : 'edit';
        const iconClass = element.value;
        const previewId = element.id === 'link-icon' ? 'icon-preview' : 'edit-icon-preview';

        return { formType, iconClass, previewId };
    }

    ensureIconListeners() {
        // Método de compatibilidade - já implementado nos métodos acima
        this.initIconPreviewListeners();
    }

    initMobileOptimizations() {
        // Otimizações específicas para mobile podem ser adicionadas aqui
        console.log('📱 Otimizações móveis ativadas');
    }
}

// ===== INICIALIZAÇÃO GLOBAL =====

// Inicializa o sistema quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.configManager = new ConfigManager();

        // Exporta para compatibilidade com código legado
        window.ConfigManager = ConfigManager;
        window.DOMManager = DOMManager;
        window.ValidationManager = ValidationManager;
        window.DebounceManager = DebounceManager;
        window.CONFIG_CONSTANTS = CONFIG_CONSTANTS;
        window.ICON_OPTIONS = ICON_OPTIONS;
        window.LINK_TYPES = LINK_TYPES;

        console.log('🚀 Sistema de Configurações do Estúdio730 carregado com sucesso!');
    } catch (error) {
        console.error('❌ Erro ao inicializar sistema de configurações:', error);
    }
});
